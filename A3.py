class USYDLibrary:
    '''
    Students are allowed to create additional attributes, methods, or subclasses if they wish,
    as long as the required interface and functionality are preserved.
    '''
    def __init__(self, shelf_capacity: int):
        """
        Initialize the library system with the given shelf capacity N.
        """
        from collections import deque

        self.shelf_capacity = shelf_capacity
        self.deque = deque  # Store reference for use in methods

        # Book inventory: book_id -> total_copies
        self.inventory = {}

        # Track borrowed books: book_id -> set of student_ids who borrowed it
        self.borrowed_books = {}

        # Pending reservations: book_id -> deque of student_ids waiting
        self.reservations = {}

        # Track who has reservations: book_id -> set of student_ids with reservations
        self.reservation_set = {}

        # Display shelf: deque of book_ids (front = highest priority)
        self.display_shelf = deque()

    def add_books(self, add_list: list[tuple[str, int]]) -> None:
        """
        Increase stock for the given titles.
        Input: A list of (book_id, q), where q represents the number of copies to be added (q ≥ 0).
        Output: None.
        """
        for book_id, quantity in add_list:
            if book_id not in self.inventory:
                self.inventory[book_id] = 0
                self.borrowed_books[book_id] = set()
                self.reservations[book_id] = self.deque()
                self.reservation_set[book_id] = set()

            self.inventory[book_id] += quantity

            # Process any pending reservations for this book
            self._process_reservations(book_id)

    def query_book_inventory(self) -> list[tuple[str, int]]:
        """
        Report current holdings per title.
        Input: None.
        Output: A list of (book_id, total_copies) for all known titles.
        """
        return [(book_id, total_copies) for book_id, total_copies in self.inventory.items()]

    def is_available(self, book_id: str) -> bool:
        """
        Determine whether one copy of a title can be lent now without violating policy.
        Input: book_id.
        Output: True if lending one copy at this moment is allowed, otherwise False.
        """
        if book_id not in self.inventory:
            return False

        total_copies = self.inventory[book_id]
        borrowed_copies = len(self.borrowed_books[book_id])
        available_copies = total_copies - borrowed_copies

        # At least one copy must remain in the library
        return available_copies > 1

    def borrow_book(self, book_id: str, student_id: str) -> bool:
        """
        Request to borrow one copy of a title.
        Input: book_id, student_id.
        Output: True if the request is fulfilled immediately,
        otherwise False and put the reservation on hold.
        """
        # Check if book exists
        if book_id not in self.inventory:
            return False

        # Check if student already has this book
        if student_id in self.borrowed_books[book_id]:
            return False

        # Check if student already has a reservation for this book
        if student_id in self.reservation_set[book_id]:
            return False

        # Check if book is available for borrowing
        if self.is_available(book_id):
            # Borrow the book
            self.borrowed_books[book_id].add(student_id)

            # Update display shelf
            self._update_display_shelf(book_id)

            return True
        else:
            # Add to reservation queue
            self.reservations[book_id].append(student_id)
            self.reservation_set[book_id].add(student_id)
            return False

    def return_book(self, book_id: str, student_id: str) -> bool:
        """
        Request to return one copy of a title.
        Input: book_id, student_id.
        Output: True if the request is fulfilled,
        False otherwise
        """
        # Check if book exists and student has borrowed it
        if book_id not in self.inventory or student_id not in self.borrowed_books[book_id]:
            return False

        # Return the book
        self.borrowed_books[book_id].remove(student_id)

        # Process any pending reservations
        self.process_reservations(book_id)

        return True

    def check_book_on_shelf(self, book_id: str) -> bool:
        """
        Determine whether a title is currently displayed on the shelf.
        Input: book_id.
        Output: True if the title is on the shelf; otherwise False.
        """
        return book_id in self.display_shelf

    def check_highest_priority_book_on_shelf(self):
        """
        Identify the title currently displayed in the front shelf position.
        Input: None.
        Output: The book_id at the highest-priority shelf position,
        or None if the shelf is empty.
        """
        if len(self.display_shelf) == 0:
            return None
        return self.display_shelf[0]

    def process_reservations(self, book_id: str) -> None:
        """
        Process pending reservations for a book when it becomes available.
        """
        # Only process one reservation at a time to avoid complexity
        if self.reservations[book_id] and self.is_available(book_id):
            # Get the next student in the reservation queue
            student_id = self.reservations[book_id].popleft()
            self.reservation_set[book_id].remove(student_id)

            # Check if student already has this book (they might have borrowed it elsewhere)
            if student_id not in self.borrowed_books[book_id]:
                # Fulfill the reservation
                self.borrowed_books[book_id].add(student_id)

                # Update display shelf
                self.update_display_shelf(book_id)
            # If student already has the book, we've removed them from queue
            # Next call to _process_reservations will handle the next student

    def update_display_shelf(self, book_id: str) -> None:
        """
        Update the display shelf when a book is borrowed.
        """
        # If book is already on shelf, move it to front
        if book_id in self.display_shelf:
            self.display_shelf.remove(book_id)

        # Add book to front of shelf
        self.display_shelf.appendleft(book_id)

        # If shelf is over capacity, remove the last book
        if len(self.display_shelf) > self.shelf_capacity:
            self.display_shelf.pop()
