#!/usr/bin/env python3

from A3 import USYDLibrary

def quick_test():
    """Quick test to verify basic functionality"""
    print("Quick test...")
    
    library = USYDLibrary(2)
    library.add_books([("book1", 3)])
    
    print(f"Inventory: {library.query_book_inventory()}")
    print(f"Available: {library.is_available('book1')}")
    
    # Borrow 2 copies (should work)
    result1 = library.borrow_book("book1", "student1")
    result2 = library.borrow_book("book1", "student2")
    print(f"Borrow 1: {result1}, Borrow 2: {result2}")
    
    # Try to borrow 3rd copy (should fail - reservation)
    result3 = library.borrow_book("book1", "student3")
    print(f"Borrow 3: {result3}")
    
    # Return one copy
    return_result = library.return_book("book1", "student1")
    print(f"Return: {return_result}")
    
    print("Quick test completed!")

if __name__ == "__main__":
    quick_test()
