#!/usr/bin/env python3

from A3 import USYDLibrary

def test_basic_functionality():
    """Test basic library functionality"""
    print("Testing USYD Library Management System...")
    
    # Initialize library with shelf capacity of 3
    library = USYDLibrary(3)
    
    # Test 1: Add books
    print("\n1. Adding books...")
    library.add_books([("book1", 3), ("book2", 2), ("book3", 1)])
    inventory = library.query_book_inventory()
    print(f"Inventory: {inventory}")
    
    # Test 2: Check availability
    print("\n2. Checking availability...")
    print(f"book1 available: {library.is_available('book1')}")  # Should be True (3 copies, need 1 in library)
    print(f"book3 available: {library.is_available('book3')}")  # Should be False (1 copy, need 1 in library)
    
    # Test 3: Borrow books
    print("\n3. Borrowing books...")
    result1 = library.borrow_book("book1", "student1")
    result2 = library.borrow_book("book1", "student2")
    result3 = library.borrow_book("book1", "student3")  # Should fail - only 1 copy left
    print(f"Student1 borrows book1: {result1}")  # True
    print(f"Student2 borrows book1: {result2}")  # True
    print(f"Student3 borrows book1: {result3}")  # False (reservation)
    
    # Test 4: Check shelf
    print("\n4. Checking display shelf...")
    print(f"book1 on shelf: {library.check_book_on_shelf('book1')}")
    print(f"Highest priority book: {library.check_highest_priority_book_on_shelf()}")
    
    # Test 5: Return book and process reservations
    print("\n5. Returning book...")
    return_result = library.return_book("book1", "student1")
    print(f"Student1 returns book1: {return_result}")
    print(f"book1 available after return: {library.is_available('book1')}")
    
    # Test 6: Test shelf capacity
    print("\n6. Testing shelf capacity...")
    library.borrow_book("book2", "student4")
    library.add_books([("book4", 2), ("book5", 2)])
    library.borrow_book("book4", "student5")
    library.borrow_book("book5", "student6")
    
    print(f"Books on shelf: {list(library.display_shelf)}")
    print(f"Shelf length: {len(library.display_shelf)} (max: {library.shelf_capacity})")
    
    print("\nAll tests completed!")

if __name__ == "__main__":
    test_basic_functionality()
