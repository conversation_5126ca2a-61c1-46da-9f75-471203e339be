#!/usr/bin/env python3

from A3 import USYDLibrary

def test_reservation_logic():
    """Test for potential infinite loops in reservation processing"""
    print("Testing reservation logic...")
    
    # Create library with small capacity
    library = USYDLibrary(2)
    
    # Add a book with only 2 copies (1 must stay in library)
    library.add_books([("book1", 2)])
    
    print(f"Initial inventory: {library.query_book_inventory()}")
    print(f"book1 available: {library.is_available('book1')}")
    
    # Student1 borrows the only available copy
    result1 = library.borrow_book("book1", "student1")
    print(f"Student1 borrows book1: {result1}")
    print(f"book1 available after student1: {library.is_available('book1')}")
    
    # Student2 tries to borrow (should go to reservation)
    result2 = library.borrow_book("book1", "student2")
    print(f"Student2 borrows book1: {result2}")
    
    # Student3 tries to borrow (should go to reservation)
    result3 = library.borrow_book("book1", "student3")
    print(f"Student3 borrows book1: {result3}")
    
    print(f"Reservations for book1: {list(library.reservations['book1'])}")
    
    # Student1 returns the book - should trigger reservation processing
    print("\nStudent1 returns book1...")
    return_result = library.return_book("book1", "student1")
    print(f"Return successful: {return_result}")
    print(f"book1 available after return: {library.is_available('book1')}")
    print(f"Remaining reservations: {list(library.reservations['book1'])}")
    
    print("Test completed successfully!")

if __name__ == "__main__":
    test_reservation_logic()
