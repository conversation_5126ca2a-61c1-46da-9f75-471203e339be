#!/usr/bin/env python3

from A3 import USYDLibrary
import time

def test_shelf_performance():
    """Test shelf update performance"""
    print("Testing shelf update performance...")
    
    start_time = time.time()
    
    # Create library with moderate shelf capacity
    library = USYDLibrary(10)
    
    # Add many books
    books = [(f"book{i}", 3) for i in range(50)]
    library.add_books(books)
    
    # Simulate many borrow operations that would update the shelf
    print("Performing many borrow operations...")
    for i in range(100):
        book_id = f"book{i % 50}"
        student_id = f"student{i}"
        result = library.borrow_book(book_id, student_id)
        if i % 20 == 0:
            print(f"Borrow {i}: {result}")
    
    elapsed = time.time() - start_time
    print(f"Test completed in {elapsed:.2f} seconds")
    
    # Check shelf state
    print(f"Shelf contents: {list(library.display_shelf)}")
    print(f"Shelf set: {library.shelf_set}")
    
    if elapsed > 10:
        print("WARNING: Test took too long, performance issue detected")
    else:
        print("SUCCESS: Performance looks good")

if __name__ == "__main__":
    test_shelf_performance()
