#!/usr/bin/env python3

from A3 import USYDLibrary
import time

def test_no_infinite_loops():
    """Test scenarios that could cause infinite loops"""
    print("Testing for infinite loops...")
    
    start_time = time.time()
    
    # Test 1: Basic functionality
    library = USYDLibrary(3)
    library.add_books([("book1", 5)])
    
    # Test 2: Multiple reservations
    library.borrow_book("book1", "s1")
    library.borrow_book("book1", "s2") 
    library.borrow_book("book1", "s3")
    library.borrow_book("book1", "s4")
    
    # Add reservation queue
    library.borrow_book("book1", "s5")  # Should go to reservation
    library.borrow_book("book1", "s6")  # Should go to reservation
    library.borrow_book("book1", "s7")  # Should go to reservation
    
    print(f"Reservations: {list(library.reservations['book1'])}")
    
    # Test 3: Return book and process reservations
    library.return_book("book1", "s1")
    print(f"After return, reservations: {list(library.reservations['book1'])}")
    
    # Test 4: Add more books (this was causing the issue)
    library.add_books([("book1", 10)])
    print(f"After adding books, reservations: {list(library.reservations['book1'])}")
    
    # Test 5: Edge case - student already has book in reservation queue
    library.add_books([("book2", 2)])
    library.borrow_book("book2", "s1")  # s1 borrows book2
    library.borrow_book("book2", "s8")  # s8 gets reservation
    
    # Now s1 tries to get another reservation for book2 (should be rejected)
    result = library.borrow_book("book2", "s1")
    print(f"s1 tries to reserve book2 while already having it: {result}")
    
    elapsed = time.time() - start_time
    print(f"Test completed in {elapsed:.2f} seconds")
    
    if elapsed > 5:
        print("WARNING: Test took too long, might indicate performance issues")
    else:
        print("SUCCESS: No infinite loops detected")

if __name__ == "__main__":
    test_no_infinite_loops()
